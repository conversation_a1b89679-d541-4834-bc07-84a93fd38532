#!/usr/bin/env python3
"""
Test script to validate the Streamlit demo components.
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_imports():
    """Test that all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Streamlit: {e}")
        return False
    
    try:
        from astrologer_prototype import AstrologerPrototype
        print("✅ AstrologerPrototype imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import AstrologerPrototype: {e}")
        return False
    
    return True

async def test_astrologer_functionality():
    """Test basic astrologer functionality."""
    print("\n🔮 Testing astrologer functionality...")
    
    try:
        from astrologer_prototype import AstrologerPrototype
        
        # Initialize astrologer
        astrologer = AstrologerPrototype(similarity_threshold=0.7)
        print("✅ Astrologer initialized")
        
        # Test a simple query
        result = await astrologer.get_astrology_response(
            "What does <PERSON> in the 7th house mean?", 
            enable_rag=True
        )
        
        if result:
            print("✅ Got astrologer response")
            print(f"   Response length: {len(result['response'])} characters")
            print(f"   RAG matches found: {result['rag_info']['matches_found']}")
            print(f"   RAG matches used: {result['rag_info']['matches_used']}")
            print(f"   Has prompt data: {'prompt_data' in result}")
            
            if 'prompt_data' in result:
                prompt_data = result['prompt_data']
                print(f"   Prompt word count: {prompt_data['word_count']}")
                print(f"   Estimated tokens: {prompt_data['estimated_tokens']}")
            
            return True
        else:
            print("❌ No response received")
            return False
            
    except Exception as e:
        print(f"❌ Error testing astrologer: {e}")
        return False

def test_streamlit_app_structure():
    """Test that the Streamlit app file is properly structured."""
    print("\n📄 Testing Streamlit app structure...")
    
    if not os.path.exists("streamlit_astrologer_demo.py"):
        print("❌ streamlit_astrologer_demo.py not found")
        return False
    
    try:
        with open("streamlit_astrologer_demo.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for key components
        required_components = [
            "st.set_page_config",
            "def main():",
            "AstrologerPrototype",
            "display_prompt_breakdown",
            "display_rag_chunks"
        ]
        
        for component in required_components:
            if component in content:
                print(f"✅ Found: {component}")
            else:
                print(f"❌ Missing: {component}")
                return False
        
        print("✅ Streamlit app structure looks good")
        return True
        
    except Exception as e:
        print(f"❌ Error reading Streamlit app: {e}")
        return False

def main():
    """Run all tests."""
    print("🌟 AI Astrologer Streamlit Demo - Test Suite")
    print("=" * 60)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed. Please check dependencies.")
        return
    
    # Test Streamlit app structure
    if not test_streamlit_app_structure():
        print("\n❌ Streamlit app structure tests failed.")
        return
    
    # Test astrologer functionality
    success = asyncio.run(test_astrologer_functionality())
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 All tests passed! The Streamlit demo should work correctly.")
        print("\n🚀 To run the demo:")
        print("   python run_streamlit_demo.py")
        print("   OR")
        print("   streamlit run streamlit_astrologer_demo.py")
    else:
        print("\n❌ Some tests failed. Please check the configuration.")

if __name__ == "__main__":
    main()
