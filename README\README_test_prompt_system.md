# 占星師 AI Prompt 測試系統

## 概述

`test_prompt.py` 是一個綜合性的測試系統，用於系統性地測試我們開發的占星師 F-string prompt 模板的效果。該系統整合了 GPT-4o 客戶端和 Pinecone RAG 檢索，提供完整的端到端測試功能。

## 核心功能

### 🧪 **完整測試套件**
- 4 種預設占星師配置 × 8 個測試問題 = 32 個完整測試
- 自動化測試流程，包含進度顯示和結果統計
- 支援 RAG 啟用/禁用模式比較
- 詳細的性能和品質評估

### 🎯 **單一配置測試**
- 針對特定占星師配置進行深度測試
- 詳細的回應分析和評估報告
- 即時的 RAG 檢索結果顯示

### ⚖️ **風格比較測試**
- 同一問題在不同占星師風格下的回應比較
- 直觀的風格差異展示
- 幫助理解不同配置的特色

### 📊 **結果導出**
- JSON 格式的詳細測試結果
- 包含完整的評估數據和元數據
- 支援後續分析和報告生成

## 系統架構

### **核心組件**

```python
# 主要類別
class AstrologerTestSystem:
    - 測試系統主控制器
    - 整合 GPT-4o 和 Pinecone 客戶端
    - 提供完整的測試流程管理

class TestConfiguration:
    - 占星師配置的數據結構
    - 包含所有可配置參數
    - 內建參數驗證功能
```

### **集成架構**

```
用戶查詢 → Pinecone RAG 檢索 → 占星師 Prompt 生成 → GPT-4o 回應 → 評估分析
    ↓              ↓                    ↓              ↓           ↓
測試問題    → 相關占星知識    → 個性化系統提示  → AI 回應    → 品質評分
```

## 預設配置

### 1. **溫暖療癒師**
- **風格**: 溫和療癒型
- **學派**: 現代心理占星
- **專精**: 本命盤解讀、情感療癒
- **字數**: 600
- **特色**: 語調溫暖、善於撫慰情緒

### 2. **學術分析師**
- **風格**: 理性分析型
- **學派**: 古典占星
- **專精**: 古典占星技法研究
- **字數**: 800
- **特色**: 邏輯清晰、客觀分析

### 3. **靈性導師**
- **風格**: 神秘智慧型
- **學派**: 進化占星
- **專精**: 靈魂成長、生命課題探索
- **字數**: 700
- **特色**: 富有詩意、具靈性深度

### 4. **實用顧問**
- **風格**: 直接犀利型
- **學派**: 西洋占星
- **專精**: 事業發展、實用建議
- **字數**: 500
- **特色**: 表達精準、重視實用性

## 測試問題設計

### **8 個多樣化測試問題**

1. **基礎星盤組合**: "我的太陽在天蠍座，月亮在雙子座，上升在處女座，這個組合如何影響我的性格？"
2. **行星流年**: "土星正在經過我的第七宮，這對我的感情關係會有什麼影響？"
3. **關係合盤**: "我是天秤座，我的伴侶是摩羯座，我們的關係相容性如何？"
4. **事業占星**: "我的火星在天蠍座第八宮，這對我的事業野心有什麼影響？"
5. **靈性發展**: "北交點在雙魚座第十二宮代表什麼靈性課題？"
6. **困難相位**: "我有太陽刑冥王星的相位，這個困難相位如何轉化為成長力量？"
7. **宮位解釋**: "第十二宮在占星學中代表什麼？有什麼重要意義？"
8. **相位分析**: "火星與木星形成三分相對個性有什麼影響？"

### **問題設計原則**
- **難度層次**: 從基礎概念到進階技法
- **風格區分**: 能夠顯現不同占星師風格差異的問題
- **RAG 依賴**: 需要專業占星知識背景的問題
- **應用範圍**: 涵蓋個人成長、關係、事業等不同領域

## 評估標準

### **字數準確性**
- 計算實際字數與目標字數的比例
- 評估範圍: 80%-120% 為良好
- 顯示: 準確率百分比

### **風格一致性**
- 基於關鍵詞匹配的風格檢測
- 評估等級: 高/中/低
- 分析: 風格特定詞彙的出現頻率

### **占星知識準確性**
- 檢測占星術語的正確使用
- 評估等級: 高/中/低
- 驗證: 基礎占星概念的準確性

### **RAG 整合度**
- 評估檢索到的背景知識的使用情況
- 分析: 相似度分數和來源引用
- 統計: 有效利用的背景資料數量

## 使用方法

### **啟動系統**
```bash
python test_prompt.py
```

### **選單操作**
```
🌟 占星師 AI 測試系統
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
請選擇測試模式:
1. 🧪 運行完整測試套件 (4配置 × 8問題 = 32測試)
2. 🎯 測試單一配置
3. ⚖️  比較特定風格
4. 🔧 自定義配置測試
5. 📊 查看預設配置
6. 📋 查看測試問題
7. ❌ 退出
```

### **完整測試套件範例**
```bash
# 選擇選項 1
1

# 選擇 RAG 模式
選擇 RAG 模式:
1. 使用 RAG (推薦)
2. 不使用 RAG
請選擇 (1-2): 1

# 系統將自動執行 32 個測試並顯示結果
```

### **單一測試範例**
```bash
# 選擇選項 2
2

# 選擇配置 (1-4)
選擇配置 (1-4): 1

# 選擇問題 (1-8)
選擇問題 (1-8): 1

# 查看詳細測試結果
```

## 輸出格式

### **即時測試結果**
```
🧪 測試配置: 溫暖療癒師
❓ 測試問題: 我的太陽在天蠍座，月亮在雙子座...

🔍 RAG 檢索結果: 找到 3 個相關資料片段 (平均相似度: 0.82)

🤖 AI 回應:
[完整回應內容...]

📊 評估結果:
   ✅ 字數: 587/600 (準確率: 98%)
   ✅ 風格一致性: 高 (關鍵詞匹配: 5)
   ✅ 占星知識: 高 (術語匹配: 8)

⏱️  響應時間: 3.2秒
```

### **測試套件總結**
```
📈 測試結果總覽
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
配置名稱        | 字數準確率 | 風格一致性 | 平均響應時間
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
溫暖療癒師      |     96.5%     |    高     |   3.1秒
學術分析師      |     94.2%     |    高     |   3.8秒
靈性導師        |     91.8%     |    中     |   4.2秒
實用顧問        |     98.1%     |    高     |   2.7秒
```

### **JSON 導出格式**
```json
{
  "test_session": {
    "timestamp": "2025-01-29T10:30:00Z",
    "configurations_tested": 4,
    "queries_per_config": 8,
    "total_tests": 32
  },
  "summary": {
    "total_tests": 32,
    "successful_tests": 30,
    "success_rate": 0.9375,
    "total_time": 125.6,
    "avg_response_time": 3.2
  },
  "results": [
    {
      "configuration": "溫暖療癒師",
      "query": "我的太陽在天蠍座...",
      "generated_prompt": "你是一位溫和療癒型...",
      "rag_info": {
        "enabled": true,
        "context_count": 3,
        "avg_similarity": 0.823
      },
      "ai_response": "...",
      "evaluation": {
        "word_count": {
          "actual": 587,
          "target": 600,
          "accuracy": 0.978
        },
        "style_consistency": {
          "level": "high",
          "keyword_matches": 5
        },
        "astrology_knowledge": {
          "level": "high",
          "term_matches": 8
        }
      },
      "response_time": 3.2,
      "timestamp": "2025-01-29T10:30:15Z"
    }
  ]
}
```

## 技術特點

### **異步處理**
- 使用 `asyncio` 進行非阻塞操作
- 支援並發測試以提高效率
- 優雅的錯誤處理和恢復機制

### **RAG 整合**
- 自動檢索相關占星學知識
- 智能相似度閾值過濾
- 多來源背景資料整合

### **錯誤處理**
- 網路連接失敗的優雅降級
- Pinecone 不可用時的備用方案
- 詳細的錯誤日誌和用戶提示

### **性能監控**
- 實時響應時間測量
- 記憶體使用優化
- 批次處理支援

## 擴展功能

### **自定義配置**
- 支援動態創建新的占星師配置
- 參數驗證和相容性檢查
- 配置模板保存和載入

### **高級評估**
- 語義相似度分析
- 情感色彩檢測
- 專業術語準確性驗證

### **報告生成**
- HTML 格式的詳細報告
- 圖表和視覺化分析
- 趨勢分析和建議

## 故障排除

### **常見問題**

1. **Pinecone 連接失敗**
   - 檢查 API 金鑰配置
   - 確認網路連接
   - 系統會自動降級到無 RAG 模式

2. **GPT-4o 回應錯誤**
   - 檢查 Azure OpenAI 配置
   - 確認 API 配額
   - 查看錯誤日誌獲取詳細信息

3. **測試結果異常**
   - 檢查配置參數的有效性
   - 確認測試問題的格式
   - 查看評估標準是否適當

### **調試模式**
```python
# 在代碼中啟用詳細日誌
import logging
logging.basicConfig(level=logging.DEBUG)
```

這個測試系統為占星師 AI prompt 模板提供了全面的品質保證和性能評估，確保生成的 AI 助手能夠提供高品質、風格一致且專業準確的占星學服務。
