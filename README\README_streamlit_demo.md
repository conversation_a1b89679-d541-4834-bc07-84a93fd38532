# 🌟 AI Astrologer Streamlit Demo

A simple, comprehensive web interface showcasing the RAG-enhanced AI astrologer system built with Streamlit.

## ✨ Features

### 🔮 **Continuous Conversation Interface**
- **Persistent Chat Sessions**: Maintain conversation context across multiple questions
- **Real-time Chat Display**: ChatGPT-like conversation flow with user questions and astrologer responses
- **Continuous Input**: Automatic input field clearing and immediate readiness for next question
- **Session Management**: Conversation history preserved throughout browser session
- **Interactive Flow**: Natural back-and-forth consultations building on previous exchanges

### 📊 **RAG Visualization & Analysis**
- Live RAG retrieval statistics
- Similarity score monitoring
- Fallback behavior demonstration
- Knowledge source transparency

### 🔍 **Complete Prompt Transparency**
- **Sectioned View**: See base persona, RAG context, and user question separately
- **Complete Prompt View**: View the full system prompt sent to GPT-4
- **Statistics**: Word count, token estimates, character count
- **Real-time Updates**: Prompt changes with each query

### ⚙️ **System Configuration**
- Adjustable similarity threshold (0.0 - 1.0)
- RAG enable/disable toggle
- Real-time system status monitoring
- Performance metrics display

## 🚀 Quick Start

### Option 1: Using the Launcher Script
```bash
python run_streamlit_demo.py
```

### Option 2: Direct Streamlit Command
```bash
# Install Streamlit if needed
pip install streamlit>=1.28.0

# Run the demo
streamlit run streamlit_astrologer_demo.py
```

### Option 3: Test First, Then Run
```bash
# Validate everything works
python test_streamlit_demo.py

# If tests pass, run the demo
python run_streamlit_demo.py
```

## 🖥️ Interface Overview

### **Enhanced Sidebar Controls**
- **Session Management**: Unique session ID and conversation tracking
- **Similarity Threshold Slider**: Real-time RAG filtering adjustment
- **RAG Toggle**: Enable/disable retrieval-augmented generation
- **Initialize/Restart Button**: Start or restart the astrologer system
- **Conversation Controls**:
  - 🗑️ Clear Chat button to reset conversation
  - Exchange counter showing total interactions
- **System Status**: Real-time connection, performance, and conversation metrics

### **Main Interface Tabs**

#### 1. **💬 Conversation Tab** (Primary Interface)
- **Continuous Chat Display**: Full conversation history in chat bubble format
- **Smart Input Form**: Auto-clearing input with immediate readiness for next question
- **Question Suggestions**: Random question button for inspiration
- **Help Integration**: Built-in tips for better astrology readings
- **Real-time Updates**: Instant conversation refresh after each exchange

#### 2. **🔍 Latest Analysis Tab** (Detailed View)
- **Current Exchange Focus**: Deep dive into the most recent question-response pair
- **RAG Metrics**: Detailed statistics on knowledge retrieval
- **Response Analysis**: Character count, processing time, and quality indicators
- **Knowledge Sources**: Display of actual RAG context used

#### 3. **📋 Prompt Inspector Tab** (Transparency)
- **📋 Sections View**:
  - 🎭 Base Astrologer Persona
  - 🔍 RAG Context (when available)
  - ❓ User Question
- **📄 Complete Prompt View**: Full system prompt sent to GPT-4
- **📊 Statistics**: Word count, tokens, characters

## 🎯 Demo Scenarios

### **Scenario 1: Continuous Conversation Flow**
1. Initialize the astrologer system
2. Ask: "What does Mars in the 7th house mean?"
3. Follow up: "How does this affect my relationships?"
4. Continue: "What about Venus aspects with Mars?"
5. Observe: Conversation builds naturally with full history preserved

### **Scenario 2: RAG Threshold Experimentation**
1. Start a conversation with threshold at 0.5
2. Ask several questions and note RAG usage
3. Change threshold to 0.8 mid-conversation
4. Ask similar questions and compare RAG behavior
5. Observe: How threshold affects knowledge retrieval in real-time

### **Scenario 3: Prompt Transparency Exploration**
1. Ask any astrology question
2. Switch to "Prompt Inspector" tab
3. Compare "Sections" vs "Complete Prompt" views
4. Ask follow-up question and see how prompt changes
5. Note: How conversation context and RAG integrate

### **Scenario 4: Session Management**
1. Have a long conversation (5+ exchanges)
2. Use "Clear Chat" to reset
3. Try "Random Question" for inspiration
4. Observe: Session persistence and conversation management

## 🔧 Technical Details

### **Architecture**
- **Frontend**: Streamlit (Python-based web framework)
- **Backend**: Direct integration with `astrologer_prototype.py`
- **RAG System**: Pinecone vector database + OpenAI embeddings
- **LLM**: GPT-4 via Azure OpenAI

### **Key Components**
- `streamlit_astrologer_demo.py` - Main Streamlit application
- `astrologer_prototype.py` - Core AI astrologer system
- `run_streamlit_demo.py` - Launcher script with auto-installation
- `test_streamlit_demo.py` - Validation test suite

### **Configuration**
- **Pinecone Index**: "astrology-text"
- **Namespace**: "hierarchical_chunking_strategy"
- **Default Threshold**: 0.7
- **Port**: 8501 (Streamlit default)

## 🎨 Visual Features

### **Astrology Theme**
- Mystical color scheme with blues and purples
- Cosmic emojis and astrology symbols
- Clean, professional layout
- Responsive design for different screen sizes

### **Interactive Elements**
- Expandable sections for detailed information
- Tabbed interface for different prompt views
- Real-time metrics and status indicators
- Copy-friendly text areas for prompt inspection

## 🔍 Understanding the System

### **RAG Pipeline Visualization**
The interface shows the complete RAG pipeline in action:
1. **User Query** → Embedding generation
2. **Vector Search** → Pinecone retrieval
3. **Similarity Filtering** → Threshold application
4. **Context Injection** → Prompt assembly
5. **LLM Generation** → AI astrologer response

### **Prompt Transparency Benefits**
- **Educational**: See how RAG enhances AI responses
- **Debugging**: Understand why certain responses are generated
- **Trust**: Complete transparency in AI decision-making
- **Optimization**: Adjust thresholds based on prompt quality

## 🚀 Next Steps

This Streamlit demo provides a solid foundation for:
- **User Testing**: Gather feedback on RAG effectiveness
- **System Tuning**: Optimize similarity thresholds
- **Feature Development**: Add new astrology-specific features
- **Integration**: Connect with additional data sources

The simple, maintainable design makes it easy to extend with new features while keeping the core functionality clear and accessible.

## 🎉 Success Metrics

The demo successfully demonstrates:
- ✅ Complete RAG pipeline functionality
- ✅ Similarity threshold filtering with fallback
- ✅ Prompt transparency and system understanding
- ✅ Professional astrologer persona integration
- ✅ Real-time system monitoring and configuration
- ✅ User-friendly interface for non-technical users
