#!/usr/bin/env python3
"""
Simple script to run the Streamlit astrologer demo.
Handles package installation and launches the interface.
"""

import subprocess
import sys
import os

def install_streamlit():
    """Install Streamlit if not available."""
    try:
        import streamlit
        print("✅ Streamlit already installed")
        return True
    except ImportError:
        print("📦 Installing Streamlit...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit>=1.28.0"])
            print("✅ Streamlit installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Streamlit: {e}")
            return False

def run_streamlit_app():
    """Run the Streamlit application."""
    try:
        print("🚀 Starting AI Astrologer Demo...")
        print("🌐 The interface will open in your browser")
        print("🔗 URL: http://localhost:8501")
        print("\n📝 Instructions:")
        print("1. Use the sidebar to configure similarity threshold")
        print("2. Click 'Initialize Astrologer' to start the system")
        print("3. Ask astrology questions in the main interface")
        print("4. View RAG analysis and prompt transparency")
        print("\n⚠️  Press Ctrl+C to stop the server")
        print("-" * 50)
        
        # Run Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "streamlit_astrologer_demo.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Demo stopped by user")
    except Exception as e:
        print(f"❌ Error running Streamlit: {e}")

def main():
    """Main function."""
    print("🌟 AI Astrologer Streamlit Demo Launcher")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("streamlit_astrologer_demo.py"):
        print("❌ streamlit_astrologer_demo.py not found in current directory")
        print("Please run this script from the project root directory")
        return
    
    if not os.path.exists("astrologer_prototype.py"):
        print("❌ astrologer_prototype.py not found in current directory")
        print("Please ensure the astrologer prototype is available")
        return
    
    # Install Streamlit if needed
    if not install_streamlit():
        return
    
    # Run the app
    run_streamlit_app()

if __name__ == "__main__":
    main()
