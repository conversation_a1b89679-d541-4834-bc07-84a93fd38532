"""
Simple PDF Chunking Strategy
Purpose: Extract and simplify the PDFChunker functionality for basic text chunking
Uses RecursiveCharacterTextSplitter from langchain_text_splitters
"""

import os
from typing import List, Dict, Any

try:
    import PyPDF2
except ImportError:
    print("Warning: PyPDF2 not installed. Please install with: pip install PyPDF2")
    PyPDF2 = None

try:
    from langchain_text_splitters import RecursiveCharacterTextSplitter
except ImportError:
    print("Warning: langchain_text_splitters not installed. Please install with: pip install langchain-text-splitters")
    RecursiveCharacterTextSplitter = None


def load_pdf(file_path: str) -> str:
    """
    Load and extract text content from PDF documents

    Args:
        file_path (str): Path to the PDF file

    Returns:
        str: Extracted text content from the PDF

    Raises:
        ImportError: If PyPDF2 is not installed
        FileNotFoundError: If the PDF file does not exist
        Exception: If PDF reading fails
    """
    if PyPDF2 is None:
        raise ImportError("PyPDF2 is required. Please install with: pip install PyPDF2")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"PDF file not found: {file_path}")

    text_content = ""

    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)

            print(f"Reading PDF: {os.path.basename(file_path)}")
            print(f"Total pages: {len(pdf_reader.pages)}")

            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content += page_text + "\n"

                    if page_num % 10 == 0:
                        print(f"Processed {page_num} pages...")

                except Exception as e:
                    print(f"Warning: Failed to read page {page_num}: {str(e)}")
                    continue

            print(f"PDF reading complete, total characters: {len(text_content)}")

    except Exception as e:
        raise Exception(f"PDF reading failed: {str(e)}")

    return text_content


def extract_paragraphs(text: str) -> List[str]:
    """
    Extract paragraphs from the loaded text content

    Args:
        text (str): Input text content

    Returns:
        List[str]: List of paragraph strings
    """
    # Split by double newlines to get paragraphs
    raw_paragraphs = text.split('\n\n')

    paragraphs = []
    for para_text in raw_paragraphs:
        # Clean paragraph text
        clean_para = para_text.strip()

        # Skip very short paragraphs or page markers
        if len(clean_para) < 20 or clean_para.isdigit():
            continue

        paragraphs.append(clean_para)

    return paragraphs


def create_metadata(chunks: List[str], source_file: str) -> List[Dict[str, Any]]:
    """
    Generate metadata for each chunk with the specified structure

    Args:
        chunks (List[str]): List of text chunks
        source_file (str): Name of the source file (not full path)

    Returns:
        List[Dict[str, Any]]: List of metadata dictionaries with structure:
            - chunk_text (str): The actual text content of the chunk
            - chunk_index (int): Sequential index number starting from 0
            - source_file (str): Name of the source file (not full path)
    """
    metadata_list = []

    for index, chunk_text in enumerate(chunks):
        metadata = {
            "chunk_text": chunk_text,
            "chunk_index": index,
            "source_file": os.path.basename(source_file)
        }
        metadata_list.append(metadata)

    return metadata_list


def process_pdf_simple(file_path: str, chunk_size: int = 250, chunk_overlap: int = 50) -> List[Dict[str, Any]]:
    """
    Process PDF file using simple chunking strategy

    Args:
        file_path (str): Path to the PDF file
        chunk_size (int): Target size for each chunk in characters
        chunk_overlap (int): Number of overlapping characters between chunks

    Returns:
        List[Dict[str, Any]]: List of chunks with metadata
    """
    if RecursiveCharacterTextSplitter is None:
        raise ImportError("langchain_text_splitters is required. Please install with: pip install langchain-text-splitters")

    # Load PDF content
    text_content = load_pdf(file_path)

    if not text_content.strip():
        print("Warning: PDF file content is empty")
        return []

    # Extract paragraphs
    paragraphs = extract_paragraphs(text_content)

    # Combine paragraphs back into text for chunking
    combined_text = "\n\n".join(paragraphs)

    # Initialize text splitter
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        length_function=len,
        separators=[
            "\n\n",   # Paragraph separator
            "\n",     # Line separator
            ". ",     # Sentence separator
            "! ",     # Exclamation separator
            "? ",     # Question separator
            "; ",     # Semicolon separator
            ", ",     # Comma separator
            " ",      # Space separator
            ""        # Character-level splitting
        ]
    )

    # Split text into chunks
    text_chunks = text_splitter.split_text(combined_text)

    print(f"Simple chunking complete, generated {len(text_chunks)} chunks")

    # Create metadata for chunks
    metadata_list = create_metadata(text_chunks, file_path)

    return metadata_list


if __name__ == "__main__":
    # Test the simple chunking strategy
    try:
        print("Testing Simple Chunking Strategy")
        print("=" * 40)

        # Test with an existing PDF file
        test_file = "./data/The Twelve Houses_ Understanding the Importance of the 12 -- by Howard Sasportas; illustrated by Jacqueline Clare.pdf"
        if os.path.exists(test_file):
            chunks = process_pdf_simple(test_file)

            if chunks:
                print(f"\nGenerated {len(chunks)} chunks")
                print(f"\nFirst chunk example:")
                first_chunk = chunks[0]
                print(f"Chunk text: {first_chunk['chunk_text'][:200]}...")
                print(f"Chunk index: {first_chunk['chunk_index']}")
                print(f"Source file: {first_chunk['source_file']}")
        else:
            print(f"Test file not found: {test_file}")
            print("Please place a PDF file in the ./data/ directory for testing")

    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()