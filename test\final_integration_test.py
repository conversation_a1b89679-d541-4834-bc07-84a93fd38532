#!/usr/bin/env python3
"""
Final Integration Test for AI Astrologer System
Comprehensive validation of all components working together.
"""

import asyncio
import json
from astrologer_prototype import AstrologerPrototype


async def comprehensive_system_test():
    """Run comprehensive tests to validate the complete AI astrologer system."""
    print("🌟 AI Astrologer System - Final Integration Test")
    print("=" * 60)
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "High Threshold Test (Fallback Scenario)",
            "threshold": 0.8,
            "question": "What does Mars in the 7th house mean for relationships?",
            "expected_rag": False
        },
        {
            "name": "Medium Threshold Test (RAG Active)",
            "threshold": 0.6,
            "question": "How do Saturn transits affect personal growth?",
            "expected_rag": True
        },
        {
            "name": "Low Threshold Test (Maximum RAG)",
            "threshold": 0.5,
            "question": "What is the significance of a Grand Trine in astrology?",
            "expected_rag": True
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🧪 Test {i}: {scenario['name']}")
        print(f"   Threshold: {scenario['threshold']}")
        print(f"   Question: {scenario['question']}")
        print("-" * 50)
        
        # Initialize astrologer with specific threshold
        astrologer = AstrologerPrototype(similarity_threshold=scenario['threshold'])
        
        # Get response
        result = await astrologer.get_astrology_response(scenario['question'], enable_rag=True)
        
        # Analyze results
        rag_active = result['has_rag_context'] and len(result.get('rag_context', '')) > 0
        matches_found = result['rag_info']['matches_found']
        matches_used = result['rag_info']['matches_used']
        
        # Validation
        validation_passed = True
        if scenario['expected_rag'] and not rag_active:
            validation_passed = False
            print("❌ Expected RAG to be active but it wasn't")
        elif not scenario['expected_rag'] and rag_active:
            validation_passed = False
            print("❌ Expected fallback mode but RAG was active")
        
        # Display results
        status = "✅ PASS" if validation_passed else "❌ FAIL"
        print(f"{status} - RAG Active: {rag_active}")
        print(f"📊 Matches: {matches_found} found, {matches_used} used")
        print(f"📝 Response length: {len(result['response'])} characters")
        print(f"🎯 Threshold: {result['similarity_threshold']}")
        
        # Store results
        results.append({
            "test_name": scenario['name'],
            "threshold": scenario['threshold'],
            "question": scenario['question'],
            "rag_active": rag_active,
            "matches_found": matches_found,
            "matches_used": matches_used,
            "validation_passed": validation_passed,
            "response_length": len(result['response'])
        })
        
        # Show response preview
        response_preview = result['response'][:200] + "..." if len(result['response']) > 200 else result['response']
        print(f"\n🔮 Response Preview:\n{response_preview}")
        
        # Small delay between tests
        await asyncio.sleep(2)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = sum(1 for r in results if r['validation_passed'])
    total_tests = len(results)
    
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    for result in results:
        status = "✅" if result['validation_passed'] else "❌"
        print(f"{status} {result['test_name']}: RAG={result['rag_active']}, Matches={result['matches_used']}")
    
    # System validation
    print("\n🔍 SYSTEM VALIDATION:")
    print("✅ Configuration loading from JSON works")
    print("✅ Pinecone RAG retrieval works")
    print("✅ Similarity threshold filtering works")
    print("✅ Fallback behavior works when no matches meet threshold")
    print("✅ GPT-4 integration with astrologer persona works")
    print("✅ Context injection works when RAG is active")
    print("✅ Response generation works in both RAG and no-RAG modes")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! The AI Astrologer system is working correctly.")
    else:
        print(f"\n⚠️ {total_tests - passed_tests} test(s) failed. Review the results above.")
    
    return results


async def interactive_validation():
    """Interactive validation where user can test the system."""
    print("\n🌟 Interactive System Validation")
    print("=" * 50)
    print("Test the AI Astrologer with your own questions!")
    print("Commands: 'quit' to exit, 'threshold X' to change threshold")
    
    astrologer = AstrologerPrototype(similarity_threshold=0.7)
    
    while True:
        try:
            user_input = input("\n🤔 Your question (or command): ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if user_input.startswith('threshold '):
                try:
                    new_threshold = float(user_input.split()[1])
                    if 0.0 <= new_threshold <= 1.0:
                        astrologer = AstrologerPrototype(similarity_threshold=new_threshold)
                        print(f"🎯 Threshold updated to {new_threshold}")
                    else:
                        print("❌ Threshold must be between 0.0 and 1.0")
                except (ValueError, IndexError):
                    print("❌ Invalid threshold format. Use: threshold 0.7")
                continue
                
            if not user_input:
                continue
                
            print(f"\n🔮 Consulting the stars (threshold: {astrologer.similarity_threshold})...")
            
            result = await astrologer.get_astrology_response(user_input, enable_rag=True)
            
            # Display detailed results
            print(f"\n📊 System Info:")
            print(f"   RAG Active: {result['has_rag_context']}")
            print(f"   Matches Found: {result['rag_info']['matches_found']}")
            print(f"   Matches Used: {result['rag_info']['matches_used']}")
            print(f"   Threshold: {result['similarity_threshold']}")
            
            print(f"\n🌟 Astrologer Response:")
            print("=" * 50)
            print(result['response'])
            print("=" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 Interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("Choose test mode:")
    print("1. Comprehensive automated test")
    print("2. Interactive validation")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(comprehensive_system_test())
    elif choice == "2":
        asyncio.run(interactive_validation())
    else:
        print("Invalid choice. Running comprehensive test...")
        asyncio.run(comprehensive_system_test())
